#!/bin/bash

echo
if [ "${1}" == "" ]; then
  echo "Last git commit id will be used as contract version, if you want to define a specific version please use: ./publish-consumer.sh [VERSION]"
  echo
fi

VERSION="${1}"
if [ "$VERSION" == "" ]; then
  VERSION=$(git rev-parse HEAD)
fi

BRANCH=$(git name-rev --name-only HEAD)

echo -e "\\n\033[34m ==========> Publish Consumer contract <==========\n\033[0m"

docker run --rm \
  -w /pacts-container \
  -v "${PWD}"/consumer/target/pacts:/pacts-container \
  --platform=linux/amd64 \
  -e PACT_BROKER_BASE_URL \
  -e PACT_BROKER_TOKEN \
  pactfoundation/pact-cli:latest \
  publish \
  . \
  --consumer-app-version "$VERSION" \
  --branch="$BRANCH" \
  --tag="$BRANCH"
